{"SERVER_ENABLED": false, "SERVER_SHORTNAME": "Us 10x", "SERVER_SPECIAL_ID": "server1", "SERVER_IP": "", "SERVER_PORT": 28016, "RCON_PORT": 28017, "RCON_PASS": "", "BOT_TOKEN": "", "BOT_CLIENT_ID": "", "USING UR PLUS PLUGIN": true, "LEADERBOARD": {"ENABLED": false, "CHANNEL_ID": "", "DEFAULT_DISPLAY(wipe, lifetime)": "lifetime", "COLOR(original, green, blue, black, yellow, orange, pink, red, purple, custom)": "custom", "CUSTOM_COLOR": "#242424"}, "SERVER_STATUS_PAGE": {"ENABLED": false, "CHANNEL_ID": "", "COLOR(original, green, blue, black, yellow, orange, pink, red, purple, custom)": "black", "CUSTOM_COLOR": "#000000"}, "WIPE_ANNOUNCEMENTS": {"ENABLED": false, "WEBHOOK": "", "EMBED_SETTINGS": {"EXTERNAL_CONTENT": "", "TITLE": "{SERVER_SHORTNAME} has wiped!", "DESCRIPTION": "**CONNECTION INFO:**\nConnect: ``{SERVER_IP}:{SERVER_PORT}``\nQuick Connect: steam://connect/{SERVER_IP}:{SERVER_PORT}\n\n**LINKS:**\n**Store:** https://store.rustmania.net/\n**Linking:** https://rustmania.net/\n**Steam:** https://steam.rustmania.net/", "SMALL_IMAGE": "", "LARGE_IMAGE": {"INFO": "If you want this to be a picture of the map, you will need to provide your rustmaps API key and it will override the large image", "DISCLAIMER": "If the map is not generated on RustMaps it will use your large image as a fall back, if there is no large image there will be no large image on the embed.", "RUSTMAPS_API_KEY": "", "LARGE_IMAGE": "https://media.discordapp.net/attachments/993380713799884901/1101551816631660704/image.png"}, "FOOTER": "Just Wiped", "EMBED_COLOR": "#4fff87"}}, "CHAT_LOGS": {"DO_YOU_USE_BETTER_CHAT": false, "SIMPLE_FORMATTING": true, "GLOBAL_CHAT_LOGS": {"ENABLED": false, "GLOBAL_CHAT_WEBHOOK": "", "EMBED_COLOR": "#00ff26"}, "TEAM_CHAT_LOGS": {"ENABLED": false, "TEAM_CHAT_WEBHOOK": "", "EMBED_COLOR": "#ff0008"}, "LOCAL_CHAT_LOGS": {"ENABLED": false, "LOCAL_CHAT_WEBHOOK": "", "EMBED_COLOR": "#fffb7d"}, "DISCORD_TO_INGAME_MESSAGES": {"ENABLED": false, "CHAT_CHANNEL_IDS": ["CHANNEL_ID", "CHANNEL_ID"], "REQUIRE_ROLES_TO_SEND_MESSAGES": false, "REQUIRED_ROLES": ["ROLE_ID", "ROLE_ID"], "MESSAGE_FORMAT": "<color=#7289DA>[DISCORD] {user}:</color>"}}, "USER_MUTING": {"INFO": "All the stuff below will require you to have Better <PERSON><PERSON> Mu<PERSON> from UMod on your server", "AUTOMATED_MUTING": {"ENABLED": false, "WATCH_TEAM_CHAT": false, "MUTE_WORDS_AND_REASONS": [{"BLOCKED_WORDS": {"EXACT_WORD_MATCHES": ["fag0t", "faggot"], "PARTIAL_WORD_MATCHES": ["fagg"]}, "MUTE_TIME (s/m/h/d)": "24h", "MUTE_REASON": "Homophobia"}, {"BLOCKED_WORDS": {"EXACT_WORD_MATCHES": ["nig"], "PARTIAL_WORD_MATCHES": ["nigge"]}, "MUTE_TIME (s/m/h/d)": "24h", "MUTE_REASON": "Racism"}], "LOG_AUTO_MUTES": true, "SIMPLE_FORMATTING": true, "LOG_WEBHOOK": "", "EMBED_COLOR": "#ff0008"}}, "DYNAMIC_MAXPLAYERS_CHANGER": {"ENABLED": true, "OPTIONS": {"DONT_CHANGE_POP_IF_FPS_IS_LESS_THAN": "15", "BASIC": {"ENABLED": true, "CONDITIONALS": [{"POP_IS_GREATER_THAN": "0", "INCREASE_MAX_PLAYERS_TO": "1"}, {"POP_IS_GREATER_THAN": "1", "INCREASE_MAX_PLAYERS_TO": "2"}, {"POP_IS_GREATER_THAN": "125", "INCREASE_MAX_PLAYERS_TO": "175"}]}, "QUEUEING": {"ENABLED": false, "NOTE": "The sections below will only change the max pop if the queue is equal to or greater then the set number.", "QUEUE_COUNT_TO_INCREASE": "10", "CONDITIONALS": [{"POP_IS_GREATER_THAN": "140", "INCREASE_MAX_PLAYERS_TO": "175"}, {"POP_IS_GREATER_THAN": "165", "INCREASE_MAX_PLAYERS_TO": "200"}]}}, "LOGGING": {"ENABLED": false, "SIMPLE_FORMATTING": false, "LOG_WEBHOOK": "", "EMBED_COLOR": "#49e637"}}, "RCON_SETTINGS": {"RCON_MESSAGE_LOGS": {"ENABLED": false, "SIMPLE_FORMATTING": false, "LOG_WEBHOOK": "", "EMBED_COLOR": "#00ff26", "DONT_SEND_RCON_MESSAGES_THAT_INCLUDE": ["NullReferenceException", "no valid dismount", "[Better Chat]", "\"Channel\":0", "\"Channel\":1"], "MESSAGE_CHUNKING_COUNT": 5}, "RCON_COMMANDS": {"ENABLED": false, "COMMAND_PREFIX": "$", "STAFF_ROLES": ["ROLE_ID", "ROLE_ID"], "COMMAND_CHANNEL_IDS": ["CHANNEL_ID", "CHANNEL_ID"], "CUSTOM_COMMANDS": {"info": "Set the command name to the first work in the command. If you want to grant permission for the kick command set the name to kick", "COMMANDS": [{"COMMAND_NAME": "kick", "REQUIRE_ROLE": false, "COMMAND_ROLE_PERMS": ["ROLE_ID"], "COMMAND_CHANNEL_IDS": ["CHANNEL_ID", "CHANNEL_ID"]}, {"COMMAND_NAME": "ban", "REQUIRE_ROLE": true, "COMMAND_ROLE_PERMS": ["ROLE_ID"], "COMMAND_CHANNEL_IDS": ["CHANNEL_ID", "CHANNEL_ID"]}, {"COMMAND_NAME": "mute", "REQUIRE_ROLE": true, "COMMAND_ROLE_PERMS": ["ROLE_ID"], "COMMAND_CHANNEL_IDS": ["CHANNEL_ID", "CHANNEL_ID"]}]}}}, "SERVER_ONLINE_OFFLINE": {"ENABLED": false, "SIMPLE_FORMATTING": false, "ONLINE_EMBED_SETTINGS": {"WEBHOOK": "", "TITLE": "{SERVER_SHORTNAME} has gone online!", "DESCRIPTION": "Join up! **************:28035", "LARGE_IMAGE": "https://cdn.discordapp.com/attachments/929569184302661682/1100159131157024798/image.png", "SMALL_IMAGE": "", "FOOTER": "SERVER ONLINE", "COLOR": "#49e637"}, "OFFLINE_EMBED_SETTINGS": {"WEBHOOK": "", "TITLE": "{SERVER_SHORTNAME} has gone offline!", "DESCRIPTION": "Please allow it time to boot up! Sorry for any inconveniences", "LARGE_IMAGE": "https://cdn.discordapp.com/attachments/929569184302661682/1100159131157024798/image.png", "SMALL_IMAGE": "", "FOOTER": "SERVER OFFLINE", "COLOR": "#eb4034"}}, "USE_POP_AS_A_BOT_STATUS": {"ENABLED": false, "OPTIONS": {"SERVER_OFFLINE_MESSAGE": "[ OFFLINE ]", "DIDNT_WIPE_TODAY": {"PLAYER_COUNT_MESSAGE": "({playersOnline}/{maxPlayers}) Online!..", "PLAYERS_JOINING_MESSAGE": "({playersOnline}/{maxPlayers}) ⇋ ({joiningPlayers} Joining!)", "PLAYERS_QUEUED_MESSAGE": "({playersOnline}/{maxPlayers}) ⇋ ({queuedPlayers} Queued!)", "ENABLE_THRESHOLD_MESSAGE": false, "THRESHOLD_PERCENT": "20", "THRESHOLD_MESSAGE": "come join!"}, "WIPED_TODAY": {"ENABLED": false, "MAX_HOURS_SINCE_LAST_WIPE": "24", "WIPED_TODAY_STATUS": {"PLAYER_COUNT_MESSAGE": "({playersOnline}/{maxPlayers}) Wiped Today!", "PLAYERS_JOINING_MESSAGE": "({playersOnline}/{maxPlayers}) ⇋ ({joiningPlayers} Joining!)", "PLAYERS_QUEUED_MESSAGE": "({playersOnline}/{maxPlayers}) ⇋ ({queuedPlayers} Queued!)", "ENABLE_THRESHOLD_MESSAGE": false, "THRESHOLD_PERCENT": "20", "THRESHOLD_MESSAGE": "Come join! Wiped today!"}}}}, "PLAYER_ACCOUNT_CHECKS": {"BAN_CHECKER": {"INFO": "This will check the player when they connect to the server if they have had a temp ban from rust", "ENABLED": true, "SIMPLE_FORMATTING": true, "THRESHOLDS": {"RUST_TEMP_BANS": 1, "VAC_BANS": 1, "EAC_BANS": 1, "DAYS_SINCE_LAST_BAN": 30}, "MENTION_STAFF_ROLES": ["ROLE_ID", "ROLE_ID"], "LOG_WEBHOOK": "", "EMBED_COLOR": "#03dffc"}}, "SERVER_LOGGING": {"F7_REPORT_LOGGING": {"ENABLED": false, "SIMPLE_FORMATTING": false, "LOG_WEBHOOK": "", "EMBED_COLOR": "#03dffc"}, "F1_SPAWN_ITEM_LOGS": {"ENABLED": false, "SIMPLE_FORMATTING": false, "LOG_WEBHOOK": "", "EMBED_COLOR": "#03dffc"}, "SERVER_JOIN_LOGS": {"ENABLED": false, "USE_PLUGIN_JOIN_LOGS": true, "SIMPLE_FORMATTING": true, "LOG_WEBHOOK": "", "EMBED_COLOR": "#03dffc"}, "SERVER_LEAVE_LOGS": {"ENABLED": false, "USE_PLUGIN_LEAVE_LOGS": true, "SIMPLE_FORMATTING": true, "LOG_WEBHOOK": "", "EMBED_COLOR": "#03dffc"}, "(SERVER)MESSAGE_LOGS": {"ENABLED": false, "LOG_WEBHOOK": "", "EMBED_COLOR": "#03dffc"}, "KILL_LOGS": {"ENABLED": false, "SIMPLE_FORMATTING": true, "USE_PLUGIN_KILL_LOGS": false, "LOG_WEBHOOK": "", "EMBED_COLOR": "#03dffc"}, "PRIVATE_MESSAGES": {"INFO": "THIS REQUIRES THE PRIVATE MESSAGES PLUGIN FROM UMOD", "ENABLED": false, "SIMPLE_FORMATTING": false, "LOG_WEBHOOK": "", "EMBED_COLOR": "#03dffc"}}, "PLUGIN_ONLY_LOGGING": {"EVENT_SPAWNS": {"ENABLED": false, "SIMPLE_FORMATTING": false, "LOG_WEBHOOK": "", "EMBED_COLOR": "#03dffc"}, "PERM_CHANGES": {"ENABLED": false, "SIMPLE_FORMATTING": false, "LOG_WEBHOOK": "", "EMBED_COLOR": "#03dffc"}, "FEEDBACK_LOGGING": {"ENABLED": false, "SIMPLE_FORMATTING": false, "LOG_WEBHOOK": "", "EMBED_COLOR": "#03dffc"}}, "BOT_VERSION": {"info": "It's recommend to not edit the version unless you know what you're doing", "version": "v3.1.0"}}